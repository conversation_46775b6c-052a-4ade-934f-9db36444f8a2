-- ABC Travels Database Setup for Supabase
-- Run this SQL in your Supabase SQL Editor

-- Create triggers for updated_at timestamps (MUST BE CREATED FIRST)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create routes table
CREATE TABLE routes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    from_location VARCHAR(100) NOT NULL,
    to_location VARCHAR(100) NOT NULL,
    departure_time TIME NOT NULL,
    arrival_time TIME NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    available_seats INTEGER DEFAULT 50,
    distance_km INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create bookings table
CREATE TABLE bookings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    route_id UUID REFERENCES routes(id),
    customer_name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    num_passengers INTEGER NOT NULL,
    travel_date DATE NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    payment_status VARCHAR(20) DEFAULT 'completed',
    booking_status VARCHAR(20) DEFAULT 'confirmed',
    payment_reference VARCHAR(100),
    ticket_image TEXT,
    special_requests TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create scans table
CREATE TABLE scans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    booking_id UUID REFERENCES bookings(id),
    scanned_by VARCHAR(100) NOT NULL,
    location VARCHAR(100) NOT NULL,
    scan_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    scan_status VARCHAR(20) NOT NULL, -- 'valid', 'invalid', 'duplicate'
    scan_message TEXT,
    device_info TEXT,
    is_first_scan BOOLEAN DEFAULT true
);

-- Create admins table
CREATE TABLE admins (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create locations table
CREATE TABLE locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create triggers for updated_at timestamps
CREATE TRIGGER update_routes_updated_at
    BEFORE UPDATE ON routes
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bookings_updated_at
    BEFORE UPDATE ON bookings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_locations_updated_at
    BEFORE UPDATE ON locations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX idx_routes_from_to ON routes(from_location, to_location);
CREATE INDEX idx_routes_active ON routes(is_active);
CREATE INDEX idx_bookings_route_id ON bookings(route_id);
CREATE INDEX idx_bookings_email ON bookings(email);
CREATE INDEX idx_bookings_status ON bookings(booking_status);
CREATE INDEX idx_bookings_payment_status ON bookings(payment_status);
CREATE INDEX idx_bookings_travel_date ON bookings(travel_date);
CREATE INDEX idx_scans_booking_id ON scans(booking_id);
CREATE INDEX idx_locations_name ON locations(name);

-- Insert default locations
INSERT INTO locations (name) VALUES
    ('Accra'),
    ('Kumasi'),
    ('Takoradi'),
    ('Tamale')
ON CONFLICT (name) DO NOTHING;

-- Insert sample routes
INSERT INTO routes (from_location, to_location, departure_time, arrival_time, price, available_seats, distance_km) VALUES
    ('Accra', 'Kumasi', '06:00:00', '10:00:00', 50.00, 50, 250),
    ('Accra', 'Kumasi', '14:00:00', '18:00:00', 50.00, 50, 250),
    ('Kumasi', 'Accra', '07:00:00', '11:00:00', 50.00, 50, 250),
    ('Kumasi', 'Accra', '15:00:00', '19:00:00', 50.00, 50, 250),
    ('Accra', 'Takoradi', '08:00:00', '12:00:00', 40.00, 50, 200),
    ('Takoradi', 'Accra', '09:00:00', '13:00:00', 40.00, 50, 200),
    ('Accra', 'Tamale', '05:00:00', '13:00:00', 80.00, 50, 600),
    ('Tamale', 'Accra', '06:00:00', '14:00:00', 80.00, 50, 600)
ON CONFLICT DO NOTHING;

-- Create ticket_emails table for email logging
CREATE TABLE ticket_emails (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    booking_id UUID REFERENCES bookings(id),
    status VARCHAR(20) NOT NULL, -- 'success', 'failed'
    recipient_email VARCHAR(100) NOT NULL,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create views for dashboard analytics
CREATE VIEW booking_overview AS
SELECT
    routes.from_location,
    routes.to_location,
    COUNT(*) as total_bookings,
    SUM(bookings.num_passengers) as total_passengers,
    SUM(bookings.total_amount) as total_revenue
FROM bookings
JOIN routes ON bookings.route_id = routes.id
WHERE bookings.payment_status = 'completed'
GROUP BY routes.from_location, routes.to_location;

CREATE VIEW revenue_overview AS
SELECT
    DATE_TRUNC('day', created_at) AS date,
    COUNT(*) as total_bookings,
    SUM(total_amount) as total_revenue,
    COUNT(DISTINCT route_id) as routes_used
FROM bookings
WHERE payment_status = 'completed'
GROUP BY DATE_TRUNC('day', created_at)
ORDER BY date DESC;

-- Create indexes for the new table
CREATE INDEX idx_ticket_emails_booking_id ON ticket_emails(booking_id);
CREATE INDEX idx_ticket_emails_status ON ticket_emails(status);

-- Note: The default admin account (username: admin, password: admin123)
-- will be created automatically when the server starts up.
