// ABC Travels - Create Admin Account <PERSON>ript
// Run this with: node create-admin.js

const bcrypt = require('bcrypt');
const { createClient } = require('@supabase/supabase-js');

// Load your Supabase credentials
const SUPABASE_URL = 'YOUR_SUPABASE_URL'; // Replace with your URL
const SUPABASE_SERVICE_KEY = 'YOUR_SUPABASE_SERVICE_KEY'; // Replace with your service role key

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function createAdmin() {
    try {
        // Get user input
        const readline = require('readline');
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        const askQuestion = (question) => {
            return new Promise((resolve) => {
                rl.question(question, (answer) => {
                    resolve(answer);
                });
            });
        };

        console.log('🚀 ABC Travels - Admin Account C<PERSON>\n');

        const username = await askQuestion('Enter username: ');
        const password = await askQuestion('Enter password: ');
        const confirmPassword = await askQuestion('Confirm password: ');

        if (password !== confirmPassword) {
            console.log('❌ Passwords do not match!');
            rl.close();
            return;
        }

        if (password.length < 6) {
            console.log('❌ Password must be at least 6 characters long!');
            rl.close();
            return;
        }

        // Check if username already exists
        const { data: existingAdmin, error: checkError } = await supabase
            .from('admins')
            .select('id')
            .eq('username', username)
            .single();

        if (existingAdmin) {
            console.log('❌ Username already exists!');
            rl.close();
            return;
        }

        // Hash the password
        console.log('🔐 Hashing password...');
        const hashedPassword = await bcrypt.hash(password, 10);

        // Create the admin account
        console.log('👤 Creating admin account...');
        const { data: newAdmin, error: createError } = await supabase
            .from('admins')
            .insert([
                {
                    username: username,
                    password_hash: hashedPassword,
                    is_active: true
                }
            ])
            .select()
            .single();

        if (createError) {
            console.log('❌ Error creating admin:', createError.message);
            rl.close();
            return;
        }

        console.log('\n✅ Admin account created successfully!');
        console.log(`👤 Username: ${username}`);
        console.log(`🔑 Password: ${password}`);
        console.log(`🆔 Admin ID: ${newAdmin.id}`);
        console.log('\n🚀 You can now login at: admin/login.html');

        rl.close();

    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

// Alternative: Create admin with predefined credentials
async function createQuickAdmin(username = 'admin', password = 'admin123') {
    try {
        console.log('🚀 Creating quick admin account...');

        // Check if admin exists
        const { data: existingAdmin } = await supabase
            .from('admins')
            .select('id')
            .eq('username', username)
            .single();

        if (existingAdmin) {
            console.log('✅ Admin account already exists!');
            console.log(`👤 Username: ${username}`);
            console.log(`🔑 Password: ${password}`);
            return;
        }

        // Hash password and create admin
        const hashedPassword = await bcrypt.hash(password, 10);

        const { data: newAdmin, error } = await supabase
            .from('admins')
            .insert([
                {
                    username: username,
                    password_hash: hashedPassword,
                    is_active: true
                }
            ])
            .select()
            .single();

        if (error) throw error;

        console.log('✅ Quick admin account created!');
        console.log(`👤 Username: ${username}`);
        console.log(`🔑 Password: ${password}`);
        console.log(`🆔 Admin ID: ${newAdmin.id}`);

    } catch (error) {
        console.error('❌ Error creating quick admin:', error.message);
    }
}

// Check command line arguments
const args = process.argv.slice(2);

if (args.includes('--quick')) {
    createQuickAdmin();
} else if (args.includes('--help')) {
    console.log(`
🚀 ABC Travels - Admin Creator

Usage:
  node create-admin.js          # Interactive mode
  node create-admin.js --quick  # Create default admin (admin/admin123)
  node create-admin.js --help   # Show this help

Before running:
1. Install dependencies: npm install bcrypt @supabase/supabase-js
2. Update SUPABASE_URL and SUPABASE_SERVICE_KEY in this file
3. Make sure your Supabase database has the admins table
    `);
} else {
    createAdmin();
}
