# ABC Travels - Complete Deployment Checklist

## 🎯 **Pre-Deployment Setup**

### ✅ **1. Supabase Configuration**
- [ ] Create Supabase project
- [ ] Run `schema.sql` in SQL Editor
- [ ] Run `supabase-rls-policies.sql` for security
- [ ] Verify all tables created (7 tables)
- [ ] Verify all views created (4 views)
- [ ] Test database connection
- [ ] Copy Project URL and Anon Key

### ✅ **2. Email Configuration**
- [ ] Set up Gmail App Password
- [ ] Test email sending locally
- [ ] Update EMAIL_USER and EMAIL_PASS

### ✅ **3. Environment Variables**
- [ ] Update `config.js` with Supabase credentials
- [ ] Create `.env` file for server
- [ ] Test local server connection

## 🚀 **Server Deployment (Render/Railway)**

### **Option A: Deploy to Render**

1. **Create Render Account**
   - Go to [render.com](https://render.com)
   - Connect your GitHub account

2. **Deploy Web Service**
   - Click "New" → "Web Service"
   - Connect your repository
   - Configure:
     - **Name**: `abc-travels-server`
     - **Environment**: `Node`
     - **Build Command**: `npm install`
     - **Start Command**: `npm start`
     - **Port**: `8080`

3. **Set Environment Variables**
   ```
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_ANON_KEY=your-anon-key
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-app-password
   PORT=8080
   NODE_ENV=production
   ```

4. **Deploy & Get URL**
   - Deploy will give you: `https://abc-travels-server.onrender.com`

### **Option B: Deploy to Railway**

1. **Create Railway Account**
   - Go to [railway.app](https://railway.app)
   - Connect GitHub

2. **Deploy Project**
   - Click "Deploy from GitHub"
   - Select repository
   - Railway auto-detects Node.js

3. **Set Environment Variables**
   - Go to Variables tab
   - Add same variables as Render above

4. **Get Deployment URL**
   - Railway provides: `https://your-app.railway.app`

## 🌐 **Frontend Deployment (Netlify)**

### **Deploy to Netlify**

1. **Prepare Frontend**
   - Update `config.js` production URLs:
   ```javascript
   production: {
     wsUrl: "wss://your-server-url.com",
     apiUrl: "https://your-server-url.com",
     supabaseUrl: "https://your-project.supabase.co",
     supabaseKey: "your-anon-key"
   }
   ```

2. **Deploy to Netlify**
   - Go to [netlify.com](https://netlify.com)
   - Drag & drop your project folder
   - Or connect GitHub repository

3. **Configure Netlify**
   - **Build Command**: Leave empty (static site)
   - **Publish Directory**: `/` (root)
   - **Site Name**: `abc-travels` (or custom)

4. **Get Frontend URL**
   - Netlify provides: `https://abc-travels.netlify.app`

## 🔧 **Post-Deployment Configuration**

### ✅ **1. Update CORS Settings**
Add your frontend domain to server CORS:
```javascript
// In server/websocket.js
app.use(cors({
  origin: [
    'http://localhost:3000',
    'https://abc-travels.netlify.app',
    'https://your-custom-domain.com'
  ]
}));
```

### ✅ **2. Test All Functionality**

**Frontend Tests:**
- [ ] Routes page loads and displays routes
- [ ] Booking form works end-to-end
- [ ] Payment integration works
- [ ] Email tickets are sent

**Admin Panel Tests:**
- [ ] Admin login works (admin/admin123)
- [ ] Dashboard loads with real data
- [ ] Routes management (CRUD operations)
- [ ] Bookings management and filtering
- [ ] QR scanner validates tickets
- [ ] Reports generate correctly

**WebSocket Tests:**
- [ ] Real-time dashboard updates
- [ ] Live booking notifications
- [ ] Scanner real-time validation

### ✅ **3. Security Verification**

**Database Security:**
- [ ] RLS policies are active
- [ ] Public can only read allowed data
- [ ] Service role has full access
- [ ] Anon role is properly restricted

**Server Security:**
- [ ] Environment variables are secure
- [ ] No sensitive data in frontend
- [ ] HTTPS is enforced
- [ ] CORS is properly configured

## 📊 **Monitoring & Maintenance**

### **Set Up Monitoring**
- [ ] Monitor server uptime (Render/Railway dashboards)
- [ ] Monitor Supabase usage and limits
- [ ] Set up error logging
- [ ] Monitor email delivery rates

### **Regular Maintenance**
- [ ] Backup database regularly
- [ ] Monitor storage usage
- [ ] Update dependencies
- [ ] Review security logs

## 🚨 **Troubleshooting Common Issues**

### **Server Won't Start**
1. Check environment variables
2. Verify Supabase credentials
3. Check server logs for errors
4. Ensure all dependencies installed

### **Database Connection Fails**
1. Verify Supabase URL and key
2. Check RLS policies
3. Test connection in Supabase dashboard
4. Verify network connectivity

### **WebSocket Connection Issues**
1. Check WSS vs WS protocol
2. Verify server URL in config.js
3. Check CORS settings
4. Test with browser dev tools

### **Email Not Sending**
1. Verify Gmail app password
2. Check EMAIL_USER format
3. Test SMTP connection
4. Review email service logs

### **Frontend Not Loading Data**
1. Check browser console for errors
2. Verify API endpoints
3. Test Supabase connection
4. Check network requests

## ✅ **Final Deployment Checklist**

**Before Going Live:**
- [ ] All tests pass
- [ ] Security policies active
- [ ] Monitoring set up
- [ ] Backup strategy in place
- [ ] Documentation updated
- [ ] Team trained on admin panel

**Production URLs:**
- [ ] Frontend: `https://abc-travels.netlify.app`
- [ ] Server: `https://abc-travels-server.onrender.com`
- [ ] Database: `https://your-project.supabase.co`

**Default Admin Access:**
- [ ] Username: `admin`
- [ ] Password: `admin123`
- [ ] Change password after first login

## 🎉 **Success Metrics**

Your deployment is successful when:
- ✅ Customers can book tickets online
- ✅ Payments process correctly
- ✅ Email tickets are delivered
- ✅ QR codes scan and validate
- ✅ Admin panel manages operations
- ✅ Real-time updates work
- ✅ Reports generate accurately

---

**🚀 Ready to launch ABC Travels!**
