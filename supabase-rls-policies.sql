-- ABC Travels - Row Level Security (RLS) Policies
-- Run this SQL in Supabase SQL Editor AFTER creating the schema

-- Enable RLS on all tables
ALTER TABLE routes ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE scans ENABLE ROW LEVEL SECURITY;
ALTER TABLE admins ENABLE ROW LEVEL SECURITY;
ALTER TABLE ticket_emails ENABLE ROW LEVEL SECURITY;
ALTER TABLE locations ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- ROUTES TABLE POLICIES
-- =====================================================

-- Allow public read access to active routes (for route display)
CREATE POLICY "Public routes read" ON routes
FOR SELECT USING (is_active = true);

-- Allow service role full access (for server operations)
CREATE POLICY "Service role routes access" ON routes
FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- BOOKINGS TABLE POLICIES  
-- =====================================================

-- Allow service role full access (server handles all booking operations)
CREATE POLICY "Service role bookings access" ON bookings
FOR ALL USING (auth.role() = 'service_role');

-- Allow anon users to create bookings (for public booking form)
CREATE POLICY "Anon booking creation" ON bookings
FOR INSERT WITH CHECK (auth.role() = 'anon');

-- =====================================================
-- SCANS TABLE POLICIES
-- =====================================================

-- Allow service role full access (server handles all scan operations)
CREATE POLICY "Service role scans access" ON scans
FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- ADMINS TABLE POLICIES
-- =====================================================

-- Allow service role full access (server handles admin authentication)
CREATE POLICY "Service role admins access" ON admins
FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- TICKET_EMAILS TABLE POLICIES
-- =====================================================

-- Allow service role full access (server handles email logging)
CREATE POLICY "Service role ticket_emails access" ON ticket_emails
FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- LOCATIONS TABLE POLICIES
-- =====================================================

-- Allow public read access (for location dropdowns)
CREATE POLICY "Public locations read" ON locations
FOR SELECT USING (true);

-- Allow service role full access (for location management)
CREATE POLICY "Service role locations access" ON locations
FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- ADDITIONAL SECURITY CONFIGURATIONS
-- =====================================================

-- Ensure views inherit proper permissions
-- (Views automatically inherit RLS from underlying tables)

-- Grant necessary permissions to anon role for public operations
GRANT SELECT ON routes TO anon;
GRANT INSERT ON bookings TO anon;
GRANT SELECT ON locations TO anon;

-- Grant full permissions to service_role (used by server)
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO service_role;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Run these to verify policies are working:

-- Test 1: Check if public can read active routes
-- SELECT * FROM routes WHERE is_active = true;

-- Test 2: Check if public can read locations  
-- SELECT * FROM locations;

-- Test 3: Verify RLS is enabled
-- SELECT schemaname, tablename, rowsecurity 
-- FROM pg_tables 
-- WHERE schemaname = 'public' AND rowsecurity = true;

-- =====================================================
-- NOTES
-- =====================================================

/*
IMPORTANT SECURITY NOTES:

1. SERVICE_ROLE KEY: 
   - Only use service_role key on your server
   - Never expose service_role key in frontend code
   - Server uses service_role for full database access

2. ANON KEY:
   - Safe to use in frontend applications
   - Limited by RLS policies defined above
   - Can only read public data and create bookings

3. RLS POLICIES:
   - Routes: Public can read active routes only
   - Bookings: Public can create, server manages all
   - Scans: Server-only access for security
   - Admins: Server-only access for authentication
   - Locations: Public read access for dropdowns

4. FRONTEND ACCESS:
   - Uses anon key for public operations
   - Server handles sensitive operations with service_role
   - WebSocket server acts as secure middleware

5. TESTING:
   - Test with anon key to ensure public access works
   - Test server operations with service_role key
   - Verify unauthorized access is blocked
*/
