const WebSocket = require("ws");
const express = require("express");
const cors = require("cors");
const { createClient } = require("@supabase/supabase-js");
const dotenv = require("dotenv");
const nodemailer = require("nodemailer");
const bcrypt = require("bcrypt");

// Load environment variables
dotenv.config();

// Initialize Express
const app = express();
app.use(cors());

// Initialize Supabase
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// Initialize WebSocket Server
const PORT = process.env.PORT || 8080;
const server = app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});

const wss = new WebSocket.Server({ server });

// Email configuration
const transporter = nodemailer.createTransport({
  service: "gmail",
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

// Database functions
async function getStats() {
  try {
    // Get today's date range
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Get total bookings
    const { count: totalBookings } = await supabase
      .from("bookings")
      .select("id", { count: "exact" })
      .eq("payment_status", "completed");

    // Get active routes
    const { count: totalRoutes } = await supabase
      .from("routes")
      .select("id", { count: "exact" })
      .eq("is_active", true);

    // Get today's revenue
    const { data: todayRevenue } = await supabase
      .from("bookings")
      .select("total_amount")
      .eq("payment_status", "completed")
      .gte("created_at", today.toISOString())
      .lt("created_at", tomorrow.toISOString());

    // Get total scanned tickets
    const { count: totalScans } = await supabase
      .from("scans")
      .select("id", { count: "exact" });

    // Calculate total revenue for today
    const todayTotalRevenue =
      todayRevenue?.reduce(
        (sum, booking) => sum + (parseFloat(booking.total_amount) || 0),
        0
      ) || 0;

    return {
      totalBookings: totalBookings || 0,
      totalRoutes: totalRoutes || 0,
      todayRevenue: todayTotalRevenue,
      totalScans: totalScans || 0,
    };
  } catch (error) {
    console.error("Error getting stats:", error);
    throw error;
  }
}

async function getDashboardData() {
  try {
    // Get basic stats
    const stats = await getStats();

    // Get recent bookings (last 10)
    const { data: recentBookings } = await supabase
      .from("bookings")
      .select(
        `
                id,
                customer_name,
                total_amount,
                created_at,
                routes (
                    from_location,
                    to_location
                )
            `
      )
      .eq("payment_status", "completed")
      .order("created_at", { ascending: false })
      .limit(10);

    // Get popular routes
    const { data: popularRoutes } = await supabase
      .from("booking_overview")
      .select("*")
      .order("total_bookings", { ascending: false })
      .limit(5);

    // Get revenue data for chart (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const { data: revenueData } = await supabase
      .from("revenue_overview")
      .select("*")
      .gte("date", sevenDaysAgo.toISOString())
      .order("date", { ascending: true });

    // Get booking trends (last 7 days)
    const { data: bookingTrends } = await supabase
      .from("revenue_overview")
      .select("date, total_bookings")
      .gte("date", sevenDaysAgo.toISOString())
      .order("date", { ascending: true });

    // Format the data for the dashboard
    return {
      stats,
      recentBookings:
        recentBookings?.map((booking) => ({
          id: booking.id,
          customer_name: booking.customer_name,
          total_amount: parseFloat(booking.total_amount),
          created_at: booking.created_at,
          routes: booking.routes,
        })) || [],
      popularRoutes:
        popularRoutes?.map((route) => ({
          from_location: route.from_location,
          to_location: route.to_location,
          total_bookings: parseInt(route.total_bookings),
          total_revenue: parseFloat(route.total_revenue),
        })) || [],
      revenueData:
        revenueData?.map((day) => ({
          date: day.date,
          revenue: parseFloat(day.total_revenue),
          bookings: parseInt(day.total_bookings),
        })) || [],
      bookingTrends:
        bookingTrends?.map((day) => ({
          date: day.date,
          bookings: parseInt(day.total_bookings),
        })) || [],
    };
  } catch (error) {
    console.error("Error getting dashboard data:", error);
    throw error;
  }
}

async function createBooking(bookingData) {
  try {
    console.log("Creating new booking:", bookingData);

    // Create the booking in the database
    const { data: booking, error: bookingError } = await supabase
      .from("bookings")
      .insert([
        {
          route_id: bookingData.route.id,
          customer_name: bookingData.customerName,
          email: bookingData.email,
          phone: bookingData.phone,
          num_passengers: bookingData.passengers,
          travel_date: bookingData.travelDate,
          total_amount: bookingData.totalAmount,
          payment_reference: bookingData.paymentReference,
          payment_status: "completed",
          booking_status: "confirmed",
          ticket_image: bookingData.ticketImage,
        },
      ])
      .select(
        `
                *,
                routes (
                    id,
                    from_location,
                    to_location,
                    departure_time
                )
            `
      )
      .single();

    if (bookingError) throw bookingError;

    console.log("Booking created successfully, sending email...");

    // Send email with ticket
    const emailResult = await sendTicketEmail({
      to: bookingData.email,
      name: bookingData.customerName,
      ticketId: booking.id,
      route: booking.routes,
      passengers: bookingData.passengers,
      ticketImage: bookingData.ticketImage,
    });

    if (!emailResult.success) {
      console.error("Failed to send ticket email:", emailResult.error);
      // Don't throw error here, just log it and continue
    }

    // Update available seats
    const { error: updateError } = await supabase
      .from("routes")
      .update({
        available_seats: supabase.raw("available_seats - ?", [
          bookingData.passengers,
        ]),
      })
      .eq("id", bookingData.route.id);

    if (updateError) throw updateError;

    return {
      success: true,
      booking: booking,
      emailSent: emailResult.success,
      emailError: emailResult.success ? null : emailResult.error,
    };
  } catch (error) {
    console.error("Error creating booking:", error);
    throw error;
  }
}

async function verifyTicket(scanData) {
  try {
    console.log("Verifying ticket:", scanData);

    if (!scanData || !scanData.bookingId) {
      return {
        success: false,
        message: "Invalid QR code format",
        scan_status: "invalid",
      };
    }

    // Check ticket validity using the database function
    const { data: validityResult, error: validityError } = await supabase.rpc(
      "check_ticket_validity",
      {
        p_booking_id: scanData.bookingId,
      }
    );

    if (validityError) {
      console.error("Error checking ticket validity:", validityError);
      return {
        success: false,
        message: "Failed to verify ticket. Please try again.",
        scan_status: "invalid",
      };
    }

    const [result] = validityResult;

    // Only record the scan if the ticket is valid
    if (result.is_valid) {
      // Record the successful scan
      const { error: scanError } = await supabase.from("scans").insert({
        booking_id: scanData.bookingId,
        scanned_by: scanData.scannedBy || "unknown",
        location: scanData.location || "unknown",
        scan_status: "valid",
        scan_message: result.message,
        device_info: scanData.deviceInfo || "unknown",
        is_first_scan: true,
      });

      if (scanError) {
        console.error("Error recording scan:", scanError);
      }

      // Update booking status to 'scanned'
      const { error: updateError } = await supabase
        .from("bookings")
        .update({ booking_status: "scanned" })
        .eq("id", scanData.bookingId);

      if (updateError) {
        console.error("Error updating booking status:", updateError);
      }
    }

    return {
      success: result.is_valid,
      message: result.message,
      scan_status: result.is_valid ? "valid" : "invalid",
      booking_data: result.booking_data,
    };
  } catch (error) {
    console.error("Error in verifyTicket:", error);
    return {
      success: false,
      message: "An error occurred while verifying the ticket",
      scan_status: "invalid",
    };
  }
}

async function getRoutes() {
  try {
    const { data: routes, error } = await supabase
      .from("routes")
      .select("*")
      .eq("is_active", true)
      .order("created_at", { ascending: false });

    if (error) throw error;
    return routes;
  } catch (error) {
    console.error("Error getting routes:", error);
    throw error;
  }
}

async function createRoute(routeData) {
  try {
    const { data: route, error } = await supabase
      .from("routes")
      .insert([
        {
          from_location: routeData.from_location,
          to_location: routeData.to_location,
          departure_time: routeData.departure_time,
          distance_km: routeData.distance_km,
          price: routeData.price,
          available_seats: routeData.available_seats,
          description: routeData.description || "",
          is_active: true,
        },
      ])
      .select()
      .single();

    if (error) throw error;
    return route;
  } catch (error) {
    console.error("Error creating route:", error);
    throw error;
  }
}

async function getBookings(filters = {}) {
  try {
    // Set default values for pagination
    const page = filters.page || 1;
    const itemsPerPage = filters.itemsPerPage || 10;

    // Build the base query
    let query = supabase.from("bookings").select(`
                *,
                routes (
                    id,
                    from_location,
                    to_location,
                    departure_time
                )
            `);

    // Apply filters if they exist
    if (filters.date) {
      query = query.eq("travel_date", filters.date);
    }
    if (filters.route) {
      query = query.eq("route_id", filters.route);
    }
    if (filters.status) {
      query = query.eq("booking_status", filters.status);
    }

    // Get total count with the same filters
    const countQuery = supabase
      .from("bookings")
      .select("*", { count: "exact", head: true });

    // Apply the same filters to the count query
    if (filters.date) {
      countQuery.eq("travel_date", filters.date);
    }
    if (filters.route) {
      countQuery.eq("route_id", filters.route);
    }
    if (filters.status) {
      countQuery.eq("booking_status", filters.status);
    }

    const { count } = await countQuery;

    // Calculate pagination
    const from = (page - 1) * itemsPerPage;
    const to = from + itemsPerPage - 1;

    // Get paginated results
    const { data: bookings, error } = await query
      .order("created_at", { ascending: false })
      .range(from, to);

    if (error) throw error;

    return {
      bookings,
      total: count || 0,
      page,
      itemsPerPage,
    };
  } catch (error) {
    console.error("Error fetching bookings:", error);
    throw error;
  }
}

async function updateRoute(routeId, updateData) {
  try {
    const { data: route, error } = await supabase
      .from("routes")
      .update(updateData)
      .eq("route_id", routeId)
      .select()
      .single();

    if (error) throw error;
    return route;
  } catch (error) {
    console.error("Error updating route:", error);
    throw error;
  }
}

async function deleteRoute(routeId) {
  try {
    const { error } = await supabase
      .from("routes")
      .update({ status: "deleted" })
      .eq("route_id", routeId);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error("Error deleting route:", error);
    throw error;
  }
}

async function getReports(type, startDate, endDate) {
  try {
    const { data: reportData, error } = await supabase
      .from(type === "revenue" ? "revenue_overview" : "booking_overview")
      .select("*")
      .gte("date", startDate)
      .lte("date", endDate);

    if (error) throw error;

    let formattedData = {
      dates: [],
      revenue: [],
      bookings: [],
      routes: [],
      routeRevenue: [],
      routeUsage: [],
      tableData: [],
      headers: [],
    };

    switch (type) {
      case "revenue":
        formattedData.headers = [
          "Date",
          "Revenue (GHS)",
          "Bookings",
          "Avg. Ticket Price",
        ];
        reportData.forEach((row) => {
          formattedData.dates.push(new Date(row.date).toLocaleDateString());
          formattedData.revenue.push(row.total_revenue);
          formattedData.bookings.push(row.total_bookings);
          formattedData.tableData.push([
            new Date(row.date).toLocaleDateString(),
            row.total_revenue.toFixed(2),
            row.total_bookings,
            (row.total_revenue / row.total_bookings).toFixed(2),
          ]);
        });
        break;

      case "routes":
        formattedData.headers = [
          "Route",
          "Total Bookings",
          "Revenue (GHS)",
          "Usage %",
        ];
        const totalBookings = reportData.reduce(
          (sum, row) => sum + row.total_bookings,
          0
        );
        reportData.forEach((row) => {
          const route = `${row.from_location} - ${row.to_location}`;
          formattedData.routes.push(route);
          formattedData.routeRevenue.push(row.total_revenue);
          const usage = ((row.total_bookings / totalBookings) * 100).toFixed(2);
          formattedData.routeUsage.push(usage);
          formattedData.tableData.push([
            route,
            row.total_bookings,
            row.total_revenue.toFixed(2),
            `${usage}%`,
          ]);
        });
        break;

      case "tickets":
        formattedData.headers = [
          "Ticket ID",
          "Customer",
          "Route",
          "Status",
          "Scan Date",
        ];
        const { data: tickets } = await supabase
          .from("bookings")
          .select(
            `
                        id,
                        customer_name,
                        routes (
                            from_location,
                            to_location
                        ),
                        scans (
                            scan_time,
                            location
                        )
                    `
          )
          .gte("created_at", startDate)
          .lte("created_at", endDate);

        tickets.forEach((ticket) => {
          const route = `${ticket.routes.from_location} - ${ticket.routes.to_location}`;
          const scanInfo = ticket.scans[0];
          formattedData.tableData.push([
            ticket.id,
            ticket.customer_name,
            route,
            scanInfo ? "Scanned" : "Not Scanned",
            scanInfo ? new Date(scanInfo.scan_time).toLocaleString() : "-",
          ]);
        });
        break;
    }

    return formattedData;
  } catch (error) {
    console.error("Error generating report:", error);
    throw error;
  }
}

async function resendTicket(bookingId) {
  try {
    // Get booking details
    const { data: booking, error: bookingError } = await supabase
      .from("bookings")
      .select(
        `
                *,
                routes (
                    from_location,
                    to_location,
                    departure_time
                )
            `
      )
      .eq("id", bookingId)
      .single();

    if (bookingError) throw bookingError;

    // Ensure we have the ticket image
    if (!booking.ticket_image) {
      // If no ticket image, generate a new one using the same format as original ticket generation
      const qrData = JSON.stringify({
        bookingId: booking.id,
        type: "ticket",
        route: {
          from: booking.routes.from_location,
          to: booking.routes.to_location,
          departure: booking.routes.departure_time,
        },
        customer: booking.customer_name,
        passengers: booking.num_passengers,
        date: booking.travel_date,
      });
      booking.ticket_image = qrData;

      // Update the booking with the new ticket image
      const { error: updateError } = await supabase
        .from("bookings")
        .update({ ticket_image: qrData })
        .eq("id", bookingId);

      if (updateError) throw updateError;
    }

    // Send email
    const emailResult = await sendTicketEmail({
      to: booking.email,
      name: booking.customer_name,
      ticketId: booking.id,
      route: booking.routes,
      passengers: booking.num_passengers,
      ticketImage: booking.ticket_image,
      isResend: true,
    });

    // Log the email attempt
    await supabase.from("ticket_emails").insert({
      booking_id: bookingId,
      status: emailResult.success ? "success" : "failed",
      error_message: emailResult.error || null,
      recipient_email: booking.email,
    });

    return {
      success: emailResult.success,
      error: emailResult.error,
    };
  } catch (error) {
    console.error("Error resending ticket:", error);
    throw error;
  }
}

async function sendTicketEmail(data) {
  try {
    const { to, name, ticketId, route, passengers, ticketImage } = data;

    console.log("Preparing to send email to:", to);

    // Create email transporter with Gmail credentials
    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
      tls: {
        rejectUnauthorized: false,
      },
    });

    // Verify transporter configuration
    try {
      await transporter.verify();
      console.log("Email transporter verified successfully");
    } catch (verifyError) {
      console.error("Email transporter verification failed:", verifyError);
      throw new Error(
        "Email configuration is invalid. Please check your credentials."
      );
    }

    // Convert base64 image to attachment
    let base64Data;
    let attachments = [];

    if (ticketImage) {
      try {
        base64Data = ticketImage.split(",")[1] || ticketImage;
        const ticketBuffer = Buffer.from(base64Data, "base64");

        attachments.push({
          filename: `ABC_Travels_Ticket_${ticketId}.png`,
          content: ticketBuffer,
          encoding: "base64",
        });
      } catch (error) {
        console.error("Error processing ticket image:", error);
        // Continue without attachment if image processing fails
      }
    }

    // Format departure time
    const departureTime = new Date(
      `2000-01-01T${route.departure_time}`
    ).toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });

    // Prepare email content with better formatting
    const mailOptions = {
      from: `"ABC Travels" <${process.env.EMAIL_USER}>`,
      to: to,
      subject: `Your ABC Travels E-Ticket - Booking #${ticketId}`,
      html: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: #1a56db; margin: 0;">Your ABC Travels E-Ticket</h1>
                    </div>
                    
                    <p style="color: #374151;">Dear ${name},</p>
                    
                    <p style="color: #374151;">Thank you for choosing ABC Travels. Your booking has been confirmed.</p>
                    
                    <div style="background-color: #f3f4f6; padding: 20px; margin: 20px 0; border-radius: 8px;">
                        <h2 style="color: #1a56db; margin-top: 0;">Booking Details</h2>
                        <table style="width: 100%; border-collapse: collapse;">
                            <tr>
                                <td style="padding: 8px 0; color: #4b5563; font-weight: bold;">Booking ID:</td>
                                <td style="padding: 8px 0; color: #1f2937;">${ticketId}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; color: #4b5563; font-weight: bold;">Route:</td>
                                <td style="padding: 8px 0; color: #1f2937;">${
                                  route.from_location
                                } → ${route.to_location}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; color: #4b5563; font-weight: bold;">Departure Time:</td>
                                <td style="padding: 8px 0; color: #1f2937;">${departureTime}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; color: #4b5563; font-weight: bold;">Number of Passengers:</td>
                                <td style="padding: 8px 0; color: #1f2937;">${passengers}</td>
                            </tr>
                        </table>
                    </div>
                    
                    <div style="margin: 20px 0; padding: 15px; background-color: #fff8e6; border-radius: 8px;">
                        <p style="color: #92400e; margin: 0;">
                            <strong>Important:</strong> ${
                              ticketImage
                                ? "Please find your QR code ticket attached to this email."
                                : "Your ticket could not be attached to this email. Please contact support for assistance."
                            } This ticket is valid for one-time use only.
                        </p>
                    </div>
                    
                    <p style="color: #374151;">Have a safe and pleasant journey!</p>
                    
                    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                        <p style="color: #6b7280; margin: 0;">Best regards,<br>ABC Travels Team</p>
                    </div>
                </div>
            `,
      attachments: attachments,
    };

    // Send email and wait for response
    console.log("Sending email...");
    const info = await transporter.sendMail(mailOptions);
    console.log("Email sent successfully:", info.response);

    // Log the successful email in the database
    await supabase.from("ticket_emails").insert([
      {
        booking_id: ticketId,
        status: "success",
        recipient_email: to,
      },
    ]);

    return { success: true };
  } catch (error) {
    console.error("Error processing ticket image or sending email:", error);
    throw error;
  }
}

// Add this function to create default admin
async function createDefaultAdmin() {
  try {
    // Check if admin exists
    const { data: existingAdmin, error: checkError } = await supabase
      .from("admins")
      .select("id")
      .eq("username", "admin")
      .single();

    if (checkError && checkError.code !== "PGRST116") throw checkError;

    // If admin doesn't exist, create one
    if (!existingAdmin) {
      const hashedPassword = await bcrypt.hash("admin123", 10);

      const { error: createError } = await supabase.from("admins").insert([
        {
          username: "admin",
          password_hash: hashedPassword,
          is_active: true,
        },
      ]);

      if (createError) throw createError;
      console.log("Default admin account created");
    }
  } catch (error) {
    console.error("Error creating default admin:", error);
  }
}

async function getRevenueAnalytics(period = "30") {
  try {
    const { data: bookings, error: bookingsError } = await supabase
      .from("bookings")
      .select(
        "id, total_amount, created_at, routes(from_location, to_location)"
      )
      .eq("payment_status", "completed");

    if (bookingsError) throw bookingsError;

    const now = new Date();
    const days = parseInt(period);
    const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

    // Filter current period bookings
    const currentPeriodBookings = bookings.filter(
      (booking) =>
        new Date(booking.created_at) >= startDate &&
        new Date(booking.created_at) <= now
    );

    // Filter previous period bookings
    const previousStartDate = new Date(
      startDate.getTime() - days * 24 * 60 * 60 * 1000
    );
    const previousPeriodBookings = bookings.filter(
      (booking) =>
        new Date(booking.created_at) >= previousStartDate &&
        new Date(booking.created_at) < startDate
    );

    // Calculate metrics
    const totalRevenue = currentPeriodBookings.reduce(
      (sum, booking) => sum + booking.total_amount,
      0
    );
    const previousRevenue = previousPeriodBookings.reduce(
      (sum, booking) => sum + booking.total_amount,
      0
    );
    const revenueChange = previousRevenue
      ? ((totalRevenue - previousRevenue) / previousRevenue) * 100
      : 0;

    // Calculate revenue by route
    const routeRevenue = {};
    currentPeriodBookings.forEach((booking) => {
      const routeKey = `${booking.routes.from_location} → ${booking.routes.to_location}`;
      if (!routeRevenue[routeKey]) {
        routeRevenue[routeKey] = {
          from_location: booking.routes.from_location,
          to_location: booking.routes.to_location,
          totalRevenue: 0,
          bookings: 0,
        };
      }
      routeRevenue[routeKey].totalRevenue += booking.total_amount;
      routeRevenue[routeKey].bookings += 1;
    });

    const revenueByRoute = Object.values(routeRevenue).map((route) => ({
      ...route,
      avgRevenuePerBooking: route.totalRevenue / route.bookings,
      percentageOfTotal: (route.totalRevenue / totalRevenue) * 100,
    }));

    // Get monthly comparison data
    const monthlyData = [];
    for (let i = 0; i < 12; i++) {
      const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);

      const thisYearRevenue = bookings
        .filter((booking) => {
          const date = new Date(booking.created_at);
          return date >= monthStart && date <= monthEnd;
        })
        .reduce((sum, booking) => sum + booking.total_amount, 0);

      const lastYearStart = new Date(
        monthStart.getFullYear() - 1,
        monthStart.getMonth(),
        1
      );
      const lastYearEnd = new Date(
        monthEnd.getFullYear() - 1,
        monthEnd.getMonth() + 1,
        0
      );

      const lastYearRevenue = bookings
        .filter((booking) => {
          const date = new Date(booking.created_at);
          return date >= lastYearStart && date <= lastYearEnd;
        })
        .reduce((sum, booking) => sum + booking.total_amount, 0);

      monthlyData.push({
        month: monthStart.toLocaleString("default", { month: "short" }),
        thisYear: thisYearRevenue,
        lastYear: lastYearRevenue,
      });
    }

    return {
      totalRevenue,
      revenueChange,
      avgDailyRevenue: totalRevenue / days,
      avgRevenueChange: revenueChange / days,
      highestRevenue: Math.max(
        ...currentPeriodBookings.map((b) => b.total_amount)
      ),
      highestRevenueDate: currentPeriodBookings.reduce((max, b) =>
        b.total_amount > (max?.total_amount || 0) ? b : max
      )?.created_at,
      revenuePerBooking: totalRevenue / currentPeriodBookings.length,
      revenuePerBookingChange: previousPeriodBookings.length
        ? ((totalRevenue / currentPeriodBookings.length -
            previousRevenue / previousPeriodBookings.length) /
            (previousRevenue / previousPeriodBookings.length)) *
          100
        : 0,
      revenueByRoute,
      monthlyComparison: monthlyData.reverse(),
    };
  } catch (error) {
    console.error("Error getting revenue analytics:", error);
    throw error;
  }
}

// Add this function after sendTicketEmail
async function sendContactEmail(data) {
  try {
    const { name, email, subject, message } = data;

    // Create email transporter
    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
      tls: {
        rejectUnauthorized: false,
      },
    });

    // Verify transporter configuration
    try {
      await transporter.verify();
      console.log("Email transporter verified successfully");
    } catch (verifyError) {
      console.error("Email transporter verification failed:", verifyError);
      throw new Error("Email configuration is invalid");
    }

    // Prepare email content
    const mailOptions = {
      from: `"${name}" <${email}>`,
      to: process.env.EMAIL_USER,
      subject: `Contact Form: ${subject}`,
      html: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #1a56db;">New Contact Form Submission</h2>
                    <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px;">
                        <p><strong>Name:</strong> ${name}</p>
                        <p><strong>Email:</strong> ${email}</p>
                        <p><strong>Subject:</strong> ${subject}</p>
                        <p><strong>Message:</strong></p>
                        <p style="white-space: pre-wrap;">${message}</p>
                    </div>
                    <p style="color: #6b7280; font-size: 0.875rem; margin-top: 20px;">
                        This message was sent from the ABC Travels contact form.
                    </p>
                </div>
            `,
    };

    // Send email
    console.log("Sending contact form email...");
    const info = await transporter.sendMail(mailOptions);
    console.log("Contact form email sent successfully:", info.response);

    return { success: true };
  } catch (error) {
    console.error("Error sending contact form email:", error);
    return { success: false, error: error.message };
  }
}

// WebSocket connection handler
async function handleWebSocketConnection(ws) {
  console.log("New WebSocket connection");

  try {
    // Set up interval for periodic updates
    const updateInterval = setInterval(async () => {
      if (ws.readyState === WebSocket.OPEN) {
        try {
          const dashboardData = await getDashboardData();
          ws.send(
            JSON.stringify({
              type: "DASHBOARD_UPDATE",
              data: dashboardData,
            })
          );
        } catch (error) {
          console.error("Error sending dashboard update:", error);
        }
      }
    }, 30000); // Update every 30 seconds

    ws.on("message", async (message) => {
      try {
        const parsedMessage = JSON.parse(message);
        console.log("Received message:", parsedMessage.type);

        switch (parsedMessage.type) {
          case "LOGIN":
            try {
              const { username, password } = parsedMessage.data;

              // Get admin user
              const { data: admin, error: adminError } = await supabase
                .from("admins")
                .select("*")
                .eq("username", username)
                .eq("is_active", true)
                .single();

              if (adminError) throw adminError;

              // Verify password
              const passwordValid = await bcrypt.compare(
                password,
                admin.password_hash
              );

              if (!passwordValid) {
                throw new Error("Invalid credentials");
              }

              // Update last login
              await supabase
                .from("admins")
                .update({ last_login: new Date().toISOString() })
                .eq("id", admin.id);

              // Send success response
              ws.send(
                JSON.stringify({
                  type: "LOGIN_RESPONSE",
                  success: true,
                  username: admin.username,
                  token: admin.id, // You might want to use a proper JWT token here
                })
              );
            } catch (error) {
              console.error("Login error:", error);
              ws.send(
                JSON.stringify({
                  type: "LOGIN_RESPONSE",
                  success: false,
                  error: "Invalid username or password",
                })
              );
            }
            break;

          case "NEW_BOOKING":
            const booking = await createBooking(parsedMessage.data);
            ws.send(
              JSON.stringify({
                type: "BOOKING_CONFIRMED",
                data: booking,
              })
            );
            broadcastUpdate();
            break;

          case "SCAN_RESULT":
            try {
              const result = await verifyTicket(parsedMessage.data);
              ws.send(
                JSON.stringify({
                  type: "VERIFICATION_RESULT",
                  data: result,
                })
              );
            } catch (error) {
              console.error("Error handling scan:", error);
              ws.send(
                JSON.stringify({
                  type: "ERROR",
                  error: "Failed to process scan. Please try again.",
                })
              );
            }
            break;

          case "GET_ROUTES":
            const routes = await getRoutes();
            ws.send(
              JSON.stringify({
                type: "ROUTES_DATA",
                data: routes,
              })
            );
            break;

          case "NEW_ROUTE":
            const newRoute = await createRoute(parsedMessage.data);
            ws.send(
              JSON.stringify({
                type: "ROUTE_CREATED",
                data: newRoute,
              })
            );
            broadcastUpdate();
            break;

          case "UPDATE_ROUTE":
            const updatedRoute = await updateRoute(
              parsedMessage.data.routeId,
              parsedMessage.data.updates
            );
            ws.send(
              JSON.stringify({
                type: "ROUTE_UPDATED",
                data: updatedRoute,
              })
            );
            broadcastUpdate();
            break;

          case "DELETE_ROUTE":
            await deleteRoute(parsedMessage.data.routeId);
            ws.send(
              JSON.stringify({
                type: "ROUTE_DELETED",
                data: { routeId: parsedMessage.data.routeId },
              })
            );
            broadcastUpdate();
            break;

          case "GET_BOOKINGS":
            try {
              const bookingsData = await getBookings(parsedMessage.filters);
              ws.send(
                JSON.stringify({
                  type: "bookings",
                  bookings: bookingsData.bookings,
                  total: bookingsData.total,
                  page: bookingsData.page,
                  itemsPerPage: bookingsData.itemsPerPage,
                })
              );
            } catch (error) {
              console.error("Error fetching bookings:", error);
              ws.send(
                JSON.stringify({
                  type: "error",
                  message: "Failed to fetch bookings: " + error.message,
                })
              );
            }
            break;

          case "GET_REPORT":
            try {
              const reportData = await getReports(
                parsedMessage.data.reportType,
                parsedMessage.data.startDate,
                parsedMessage.data.endDate
              );
              ws.send(
                JSON.stringify({
                  type: "REPORT_DATA",
                  data: reportData,
                })
              );
            } catch (error) {
              ws.send(
                JSON.stringify({
                  type: "ERROR",
                  error: "Failed to generate report: " + error.message,
                })
              );
            }
            break;

          case "RESEND_TICKET":
            try {
              const result = await resendTicket(parsedMessage.data.booking_id);
              ws.send(
                JSON.stringify({
                  type: "TICKET_RESEND_RESULT",
                  success: result.success,
                  error: result.error,
                })
              );
            } catch (error) {
              ws.send(
                JSON.stringify({
                  type: "ERROR",
                  error: "Failed to resend ticket: " + error.message,
                })
              );
            }
            break;

          case "SEND_TICKET":
            try {
              console.log("Received SEND_TICKET request:", parsedMessage.data);
              const emailResult = await sendTicketEmail({
                to: parsedMessage.data.email,
                name: parsedMessage.data.name,
                ticketId: parsedMessage.data.bookingDetails.id,
                route: {
                  from_location:
                    parsedMessage.data.bookingDetails.route.split(" → ")[0],
                  to_location:
                    parsedMessage.data.bookingDetails.route.split(" → ")[1],
                  departure_time:
                    parsedMessage.data.bookingDetails.departureTime,
                },
                passengers: 1,
                ticketImage: parsedMessage.data.ticketImage,
              });

              ws.send(
                JSON.stringify({
                  type: "TICKET_SENT",
                  success: emailResult.success,
                  error: emailResult.error,
                })
              );
            } catch (error) {
              console.error("Error sending ticket:", error);
              ws.send(
                JSON.stringify({
                  type: "ERROR",
                  error: "Failed to send ticket: " + error.message,
                })
              );
            }
            break;

          case "GET_REVENUE_ANALYTICS":
            try {
              const analytics = await getRevenueAnalytics(
                parsedMessage.data?.period
              );
              ws.send(
                JSON.stringify({
                  type: "REVENUE_ANALYTICS",
                  data: analytics,
                })
              );
            } catch (error) {
              ws.send(
                JSON.stringify({
                  type: "ERROR",
                  error: "Failed to get revenue analytics",
                })
              );
            }
            break;

          case "REQUEST_UPDATE":
            try {
              const dashboardData = await getDashboardData();
              const revenueAnalytics = await getRevenueAnalytics(
                parsedMessage.data?.period || "30"
              );

              ws.send(
                JSON.stringify({
                  type: "DASHBOARD_UPDATE",
                  data: {
                    ...dashboardData,
                    revenueAnalytics,
                  },
                })
              );
            } catch (error) {
              console.error("Error updating dashboard:", error);
              ws.send(
                JSON.stringify({
                  type: "ERROR",
                  error: "Failed to update dashboard",
                })
              );
            }
            break;

          case "EXPORT_REVENUE_REPORT":
            try {
              const { period } = parsedMessage.data;
              const analytics = await getRevenueAnalytics(period);
              const reportData = {
                generatedAt: new Date().toISOString(),
                period: `Last ${period} Days`,
                metrics: {
                  totalRevenue: analytics.totalRevenue,
                  avgDailyRevenue: analytics.avgDailyRevenue,
                  revenueChange: analytics.revenueChange,
                  highestRevenue: analytics.highestRevenue,
                  highestRevenueDate: analytics.highestRevenueDate,
                  revenuePerBooking: analytics.revenuePerBooking,
                },
                revenueByRoute: analytics.revenueByRoute,
                monthlyComparison: analytics.monthlyComparison,
              };

              ws.send(
                JSON.stringify({
                  type: "REVENUE_REPORT",
                  data: reportData,
                })
              );
            } catch (error) {
              console.error("Error generating revenue report:", error);
              ws.send(
                JSON.stringify({
                  type: "ERROR",
                  error: "Failed to generate revenue report",
                })
              );
            }
            break;

          case "CONTACT_FORM":
            try {
              const result = await sendContactEmail(parsedMessage.data);
              ws.send(
                JSON.stringify({
                  type: "CONTACT_FORM_RESPONSE",
                  success: result.success,
                  error: result.error,
                })
              );
            } catch (error) {
              console.error("Error processing contact form:", error);
              ws.send(
                JSON.stringify({
                  type: "CONTACT_FORM_RESPONSE",
                  success: false,
                  error: "Failed to send message. Please try again.",
                })
              );
            }
            break;
        }
      } catch (error) {
        console.error("Error handling message:", error);
        ws.send(
          JSON.stringify({
            type: "ERROR",
            error: "Failed to process request",
          })
        );
      }
    });

    ws.on("close", () => {
      clearInterval(updateInterval);
    });
  } catch (error) {
    console.error("Error in WebSocket connection:", error);
  }
}

// Set up WebSocket server
wss.on("connection", handleWebSocketConnection);

// Broadcast updates to all connected clients
async function broadcastUpdate() {
  try {
    const data = await getDashboardData();
    wss.clients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(
          JSON.stringify({
            type: "DASHBOARD_UPDATE",
            data,
          })
        );
      }
    });
  } catch (error) {
    console.error("Error broadcasting update:", error);
  }
}

// Call createDefaultAdmin when server starts
setTimeout(() => {
  createDefaultAdmin();
}, 2000); // Wait 2 seconds for database connection to be ready
