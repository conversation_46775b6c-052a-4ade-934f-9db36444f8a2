# ABC Travels - Supabase Setup & Configuration Guide

## 🚀 Quick Setup Checklist

### ✅ **STEP 1: Supabase Project Setup**

1. **Create New Supabase Project**
   - Go to [supabase.com](https://supabase.com)
   - Click "New Project"
   - Choose organization and set project name: `abc-travels`
   - Set database password (save this!)
   - Select region closest to your users

2. **Get Project Credentials**
   - Go to Project Settings → API
   - Copy these values:
     - **Project URL**: `https://your-project-ref.supabase.co`
     - **Anon Public Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

### ✅ **STEP 2: Database Schema Setup**

1. **Run the Schema**
   - Go to SQL Editor in Supabase Dashboard
   - Copy the entire `schema.sql` file content
   - Paste and click "Run"
   - ✅ This creates all tables, views, functions, and indexes

2. **Verify Tables Created**
   - Go to Table Editor
   - You should see: `routes`, `bookings`, `scans`, `admins`, `ticket_emails`, `locations`

### ✅ **STEP 3: Row Level Security (RLS) Configuration**

**IMPORTANT**: You need to configure RLS policies for security:

1. **Go to Authentication → Policies**
2. **For each table, create these policies:**

#### Routes Table Policies:
```sql
-- Allow public read access to active routes
CREATE POLICY "Public routes read" ON routes
FOR SELECT USING (is_active = true);

-- Allow service role full access
CREATE POLICY "Service role full access" ON routes
FOR ALL USING (auth.role() = 'service_role');
```

#### Bookings Table Policies:
```sql
-- Allow service role full access
CREATE POLICY "Service role full access" ON bookings
FOR ALL USING (auth.role() = 'service_role');
```

#### Scans Table Policies:
```sql
-- Allow service role full access  
CREATE POLICY "Service role full access" ON scans
FOR ALL USING (auth.role() = 'service_role');
```

#### Admins Table Policies:
```sql
-- Allow service role full access
CREATE POLICY "Service role full access" ON admins
FOR ALL USING (auth.role() = 'service_role');
```

#### Locations Table Policies:
```sql
-- Allow public read access
CREATE POLICY "Public locations read" ON locations
FOR SELECT USING (true);

-- Allow service role full access
CREATE POLICY "Service role full access" ON locations
FOR ALL USING (auth.role() = 'service_role');
```

#### Ticket Emails Table Policies:
```sql
-- Allow service role full access
CREATE POLICY "Service role full access" ON ticket_emails
FOR ALL USING (auth.role() = 'service_role');
```

### ✅ **STEP 4: Environment Variables Setup**

1. **Update `config.js`**
   ```javascript
   const config = {
     development: {
       wsUrl: "ws://localhost:8080",
       apiUrl: "http://localhost:8080",
       supabaseUrl: "YOUR_SUPABASE_PROJECT_URL",
       supabaseKey: "YOUR_SUPABASE_ANON_KEY"
     },
     production: {
       wsUrl: "wss://your-deployed-server.com",
       apiUrl: "https://your-deployed-server.com", 
       supabaseUrl: "YOUR_SUPABASE_PROJECT_URL",
       supabaseKey: "YOUR_SUPABASE_ANON_KEY"
     }
   };
   ```

2. **Create `.env` file for server**
   ```env
   SUPABASE_URL=YOUR_SUPABASE_PROJECT_URL
   SUPABASE_ANON_KEY=YOUR_SUPABASE_ANON_KEY
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-app-password
   PORT=8080
   ```

### ✅ **STEP 5: Email Configuration**

1. **Gmail App Password Setup**
   - Enable 2FA on your Gmail account
   - Go to Google Account Settings → Security → App Passwords
   - Generate app password for "Mail"
   - Use this password in `EMAIL_PASS`

2. **Update Email Settings**
   - The server uses Gmail SMTP by default
   - Update `EMAIL_USER` and `EMAIL_PASS` in `.env`

### ✅ **STEP 6: Test the Setup**

1. **Start the Server**
   ```bash
   npm install
   npm start
   ```

2. **Test Admin Login**
   - Go to `admin/login.html`
   - Username: `admin`
   - Password: `admin123`
   - ✅ Should create default admin automatically

3. **Test Database Connection**
   - Dashboard should load with stats
   - Routes should display in routes.html
   - Booking system should work

## 🔧 **Additional Supabase Configurations**

### **API Settings**
- **Auto API Documentation**: Enable
- **API URL**: Use in your frontend
- **GraphQL**: Optional (not used in this project)

### **Authentication Settings**
- **Enable Email Auth**: Optional (not used for admin)
- **Site URL**: Set to your domain
- **Redirect URLs**: Add your domains

### **Storage Settings** (Optional)
- Create bucket for ticket images if needed
- Set up policies for public access

## 🚨 **Security Considerations**

1. **RLS Policies**: Essential for data security
2. **Service Role Key**: Keep secret, only use server-side
3. **Anon Key**: Safe for frontend use
4. **Database Password**: Store securely
5. **Email Credentials**: Use app passwords, not main password

## 📊 **Database Schema Overview**

### **Tables Created:**
- `routes` - Bus routes with pricing and schedules
- `bookings` - Customer bookings and payments  
- `scans` - QR code scan records
- `admins` - Admin user accounts
- `ticket_emails` - Email delivery logs
- `locations` - Available cities/destinations

### **Views Created:**
- `booking_overview` - Route booking statistics
- `revenue_overview` - Daily revenue analytics
- `email_stats` - Email delivery statistics  
- `revenue_analytics` - Comprehensive revenue analysis

### **Functions Created:**
- `check_ticket_validity()` - Validates QR codes
- `get_revenue_analytics()` - Revenue calculations
- `update_updated_at_column()` - Timestamp triggers

## 🔍 **Troubleshooting**

### **Common Issues:**

1. **"relation does not exist" error**
   - Run the schema.sql again
   - Check if all tables were created

2. **RLS policy errors**
   - Add the RLS policies above
   - Check policy syntax

3. **WebSocket connection fails**
   - Check server environment variables
   - Verify Supabase credentials

4. **Email not sending**
   - Check Gmail app password
   - Verify EMAIL_USER and EMAIL_PASS

5. **Admin login fails**
   - Server creates admin on startup
   - Check server logs for errors

## ✅ **Final Verification**

After setup, verify these work:
- [ ] Admin dashboard loads with data
- [ ] Routes display on public page
- [ ] Booking system processes payments
- [ ] QR scanner validates tickets
- [ ] Email tickets are sent
- [ ] Reports generate correctly

## 🎯 **Next Steps**

1. Deploy server to production (Render, Railway, etc.)
2. Deploy frontend to Netlify/Vercel
3. Update production URLs in config
4. Test end-to-end functionality
5. Set up monitoring and backups

---

**Need Help?** Check the troubleshooting section or review server logs for specific error messages.
