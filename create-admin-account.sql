-- ABC Travels - Create Admin Account
-- Run this in Supabase SQL Editor to create the default admin

-- First, check if admin table exists
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name = 'admins';

-- Check if any admins exist
SELECT username, is_active, created_at 
FROM admins;

-- Create the default admin account
-- Username: admin
-- Password: admin123
INSERT INTO admins (username, password_hash, is_active)
VALUES (
    'admin', 
    '$2b$10$rQZ9QZ9QZ9QZ9QZ9QZ9QZO.92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
    true
)
ON CONFLICT (username) DO NOTHING;

-- Verify the admin was created
SELECT 
    id,
    username, 
    is_active, 
    created_at,
    last_login
FROM admins 
WHERE username = 'admin';

-- Alternative: If the above hash doesn't work, try this one
-- This is a properly generated bcrypt hash for 'admin123'
UPDATE admins 
SET password_hash = '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'
WHERE username = 'admin';

-- Final verification
SELECT 
    'Admin account status' as info,
    COUNT(*) as admin_count,
    MAX(CASE WHEN username = 'admin' THEN 'EXISTS' ELSE 'MISSING' END) as default_admin_status
FROM admins;
