-- ABC Travels - Sample Data for Testing Dashboard & Bookings
-- Run this in Supabase SQL Editor AFTER running schema.sql

-- Insert sample routes (if not already exists)
INSERT INTO routes (from_location, to_location, departure_time, price, available_seats, distance_km, description, is_active) 
VALUES 
    ('Accra', 'Kumasi', '06:00:00', 50.00, 50, 250, 'Morning express service', true),
    ('Accra', 'Kumasi', '14:00:00', 50.00, 50, 250, 'Afternoon service', true),
    ('Kumasi', 'Accra', '07:00:00', 50.00, 50, 250, 'Morning return service', true),
    ('<PERSON>masi', 'Accra', '15:00:00', 50.00, 50, 250, 'Afternoon return service', true),
    ('Accra', 'Takoradi', '08:00:00', 40.00, 50, 200, 'Coastal route', true),
    ('Takoradi', 'Accra', '09:00:00', 40.00, 50, 200, 'Return coastal route', true),
    ('<PERSON>ccra', 'Tamale', '05:00:00', 80.00, 50, 600, 'Northern route - early departure', true),
    ('Tamale', 'Accra', '06:00:00', 80.00, 50, 600, 'Southern route from North', true)
ON CONFLICT DO NOTHING;

-- Insert sample bookings for testing dashboard
INSERT INTO bookings (
    route_id, 
    customer_name, 
    email, 
    phone, 
    num_passengers, 
    travel_date, 
    total_amount, 
    payment_reference,
    payment_status, 
    booking_status,
    created_at
) 
SELECT 
    r.id,
    names.name,
    names.email,
    names.phone,
    (RANDOM() * 3 + 1)::INTEGER, -- 1-4 passengers
    CURRENT_DATE + (RANDOM() * 30)::INTEGER, -- Next 30 days
    r.price * (RANDOM() * 3 + 1)::INTEGER, -- Price * passengers
    'PAY_' || UPPER(SUBSTR(MD5(RANDOM()::TEXT), 1, 10)),
    'completed',
    'confirmed',
    CURRENT_TIMESTAMP - (RANDOM() * INTERVAL '30 days') -- Last 30 days
FROM routes r
CROSS JOIN (
    VALUES 
        ('John Doe', '<EMAIL>', '0241234567'),
        ('Jane Smith', '<EMAIL>', '0242345678'),
        ('Michael Johnson', '<EMAIL>', '0243456789'),
        ('Sarah Wilson', '<EMAIL>', '0244567890'),
        ('David Brown', '<EMAIL>', '0245678901'),
        ('Lisa Davis', '<EMAIL>', '0246789012'),
        ('Robert Miller', '<EMAIL>', '0247890123'),
        ('Emily Taylor', '<EMAIL>', '0248901234'),
        ('James Anderson', '<EMAIL>', '0249012345'),
        ('Maria Garcia', '<EMAIL>', '0240123456'),
        ('Christopher Lee', '<EMAIL>', '0241234568'),
        ('Amanda White', '<EMAIL>', '0242345679'),
        ('Daniel Harris', '<EMAIL>', '0243456780'),
        ('Jennifer Clark', '<EMAIL>', '0244567891'),
        ('Matthew Lewis', '<EMAIL>', '0245678902')
) AS names(name, email, phone)
WHERE RANDOM() < 0.3 -- Only insert ~30% to avoid too much data
LIMIT 50;

-- Insert some scan records for testing
INSERT INTO scans (
    booking_id,
    scanned_by,
    location,
    scan_status,
    scan_message,
    device_info,
    is_first_scan,
    scan_time
)
SELECT 
    b.id,
    'Scanner Admin',
    CASE 
        WHEN r.from_location = 'Accra' THEN 'Accra Terminal'
        WHEN r.from_location = 'Kumasi' THEN 'Kumasi Terminal'
        WHEN r.from_location = 'Takoradi' THEN 'Takoradi Terminal'
        ELSE 'Tamale Terminal'
    END,
    'valid',
    'Ticket validated successfully',
    'Scanner Device v1.0',
    true,
    b.created_at + INTERVAL '1 hour'
FROM bookings b
JOIN routes r ON b.route_id = r.id
WHERE RANDOM() < 0.4 -- Only scan ~40% of bookings
LIMIT 20;

-- Create default admin account (if not exists)
INSERT INTO admins (username, password_hash, is_active)
SELECT 'admin', '$2b$10$rQZ9QZ9QZ9QZ9QZ9QZ9QZO', true
WHERE NOT EXISTS (SELECT 1 FROM admins WHERE username = 'admin');

-- Insert some ticket email records
INSERT INTO ticket_emails (
    booking_id,
    status,
    recipient_email,
    sent_at
)
SELECT 
    b.id,
    CASE WHEN RANDOM() < 0.9 THEN 'success' ELSE 'failed' END,
    b.email,
    b.created_at + INTERVAL '5 minutes'
FROM bookings b
WHERE RANDOM() < 0.8 -- 80% of bookings got emails
LIMIT 40;

-- Verify data was inserted
SELECT 
    'Routes' as table_name, 
    COUNT(*) as record_count 
FROM routes
UNION ALL
SELECT 
    'Bookings' as table_name, 
    COUNT(*) as record_count 
FROM bookings
UNION ALL
SELECT 
    'Scans' as table_name, 
    COUNT(*) as record_count 
FROM scans
UNION ALL
SELECT 
    'Admins' as table_name, 
    COUNT(*) as record_count 
FROM admins
UNION ALL
SELECT 
    'Ticket Emails' as table_name, 
    COUNT(*) as record_count 
FROM ticket_emails
UNION ALL
SELECT 
    'Locations' as table_name, 
    COUNT(*) as record_count 
FROM locations;

-- Test the views work
SELECT 'booking_overview' as view_name, COUNT(*) as records FROM booking_overview
UNION ALL
SELECT 'revenue_overview' as view_name, COUNT(*) as records FROM revenue_overview;

-- Show sample dashboard data
SELECT 
    'Dashboard Preview' as info,
    (SELECT COUNT(*) FROM bookings WHERE payment_status = 'completed') as total_bookings,
    (SELECT COUNT(*) FROM routes WHERE is_active = true) as active_routes,
    (SELECT COUNT(*) FROM scans) as total_scans,
    (SELECT COALESCE(SUM(total_amount), 0) FROM bookings WHERE payment_status = 'completed' AND DATE(created_at) = CURRENT_DATE) as today_revenue;
